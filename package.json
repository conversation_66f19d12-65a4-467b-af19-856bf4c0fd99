{"name": "gemini-image-gen-mcp-server", "version": "1.0.0", "description": "MCP server for Google Gemini image generation", "type": "module", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js"}, "dependencies": {"@google/genai": "^0.8.0", "@google/generative-ai": "^0.2.1", "@modelcontextprotocol/sdk": "^0.6.0"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}